$ next build
   ▲ Next.js 15.3.4

   Creating an optimized production build ...
 ✓ Compiled successfully in 3.0s
   Linting and checking validity of types ...
Failed to compile.

./components/settings/SettingsPanel.tsx:30:18
Type error: Type 'string' is not assignable to type '"showProjectPanel" | "showTextEditPanel" | "showOCRPanel" | "showTranslationPanel" | "showGrid" | "showRulers" | "sidebarWidth" | "rightPanelWidth"'.

[0m [90m 28 |[39m     dispatch({[0m
[0m [90m 29 |[39m       type[33m:[39m [32m'SET_PANEL_VISIBILITY'[39m[33m,[39m[0m
[0m[31m[1m>[22m[39m[90m 30 |[39m       payload[33m:[39m { panel[33m:[39m key[33m,[39m visible[33m:[39m value }[0m
[0m [90m    |[39m                  [31m[1m^[22m[39m[0m
[0m [90m 31 |[39m     })[33m;[39m[0m
[0m [90m 32 |[39m   }[33m;[39m[0m
[0m [90m 33 |[39m[0m
Next.js build worker exited with code: 1 and signal: null
error: script "build" exited with code 1
