'use client';

import { Html } from 'react-konva-utils';
import { useEffect, useRef } from 'react';
import Konva from 'konva';

// Fix text rendering issues
(Konva as any)._fixTextRendering = true;

interface InlineTextEditorProps {
  textNode: any; // Konva Text node
  region: any; // Text region data with dimensions
  initialText?: string; // Initial text content to display
  clickPosition?: { x: number; y: number } | null; // Click position relative to region
  onClose: () => void;
  onChange: (text: string) => void;
}

export default function InlineTextEditor({ textNode, region, initialText, clickPosition, onClose, onChange }: InlineTextEditorProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Calculate styles immediately using region data (simple approach like ExampleTextEditor)
  const regionWidth = region?.width || 100;
  const regionHeight = region?.height || 50;
  const regionX = region?.x || 0;
  const regionY = region?.y || 0;
  const padding = textNode?.padding() || 0;
  const rotation = textNode?.rotation() || 0;

  const textAlign = textNode?.align() || 'center';

  // Calculate vertical padding to center the text
  const fontSize = textNode?.fontSize() || 14;
  const textHeight = fontSize * (textNode?.lineHeight() || 1.2);
  const availableHeight = regionHeight - padding * 2;
  const verticalPadding = Math.max(0, (availableHeight - textHeight) / 2);

  const textareaStyles = {
    position: 'absolute' as const,
    top: `${regionY}px`,
    left: `${regionX}px`,
    width: `${regionWidth - padding * 2}px`,
    height: `${regionHeight - padding * 2}px`,
    fontSize: `${fontSize}px`,
    border: 'none',
    padding: `${verticalPadding}px 0px`,
    margin: '0px',
    overflow: 'hidden' as const,
    background: 'none',
    outline: 'none',
    resize: 'none' as const,
    lineHeight: textNode?.lineHeight() || '1.2',
    fontFamily: textNode?.fontFamily() || 'Arial',
    transformOrigin: 'left top' as const,
    textAlign: textAlign as 'left' | 'center' | 'right' | 'justify',
    color: textNode?.fill() || '#000000',
    transform: rotation ? `rotateZ(${rotation}deg)` : '',
    minHeight: '1em',
    boxSizing: 'border-box' as const,
  };

  // Calculate cursor position from click coordinates
  const calculateCursorPosition = (textarea: HTMLTextAreaElement, clickX: number, clickY: number): number => {
    const text = textarea.value;
    const style = window.getComputedStyle(textarea);
    const fontSize = parseFloat(style.fontSize);
    const lineHeight = parseFloat(style.lineHeight) || fontSize * 1.2;

    // Calculate which line was clicked
    const padding = parseFloat(style.paddingTop) || 0;
    const relativeY = clickY - padding;
    const lineIndex = Math.max(0, Math.floor(relativeY / lineHeight));

    // Split text into lines
    const lines = text.split('\n');
    if (lineIndex >= lines.length) {
      return text.length; // Click was below text, position at end
    }

    // Calculate character position within the line
    const line = lines[lineIndex];
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) return 0;

    ctx.font = `${fontSize}px ${style.fontFamily}`;

    // Find the character position closest to the click
    let charPosition = 0;
    let minDistance = Infinity;

    for (let i = 0; i <= line.length; i++) {
      const textWidth = ctx.measureText(line.substring(0, i)).width;
      const distance = Math.abs(textWidth - (clickX - parseFloat(style.paddingLeft || '0')));

      if (distance < minDistance) {
        minDistance = distance;
        charPosition = i;
      }
    }

    // Calculate absolute position in the full text
    let absolutePosition = 0;
    for (let i = 0; i < lineIndex; i++) {
      absolutePosition += lines[i].length + 1; // +1 for newline
    }
    absolutePosition += charPosition;

    return Math.min(absolutePosition, text.length);
  };

  // Focus and position cursor based on click position
  useEffect(() => {
    if (!textareaRef.current) return;

    const textarea = textareaRef.current;

    // Use requestAnimationFrame to ensure the textarea is fully rendered before focusing
    const focusAndPositionCursor = () => {
      requestAnimationFrame(() => {
        if (textarea && document.contains(textarea)) {
          textarea.focus();

          if (clickPosition) {
            // Calculate cursor position based on click coordinates
            const cursorPos = calculateCursorPosition(textarea, clickPosition.x, clickPosition.y);
            textarea.setSelectionRange(cursorPos, cursorPos);
          } else {
            // Fallback: select all text if no click position
            textarea.select();
          }
        }
      });
    };

    // Try to focus immediately
    focusAndPositionCursor();

    // Also try after a short delay as fallback
    const timeoutId = setTimeout(focusAndPositionCursor, 10);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [clickPosition]); // Include clickPosition in dependencies

  // Handle event listeners
  useEffect(() => {
    if (!textareaRef.current || !textNode) return;

    const textarea = textareaRef.current;

    // Track if we've added the outside click listener
    let outsideClickAdded = false;
    let canvasClickAdded = false;

    const handleOutsideClick = (e: MouseEvent) => {
      // Check if the click target is the textarea or its descendants
      const target = e.target as Element;
      if (target && textarea) {
        // Check if click is outside textarea
        const isOutsideTextarea = target !== textarea && !textarea.contains(target);

        // Check if click is on canvas, editor elements, or canvas container
        const isOnCanvas = target.tagName === 'CANVAS' || target.closest('canvas');
        const isOnEditor = target.closest('[data-editor]') || target.closest('.konvajs-content');
        const isOnCanvasContainer = target.closest('[data-canvas-container]');

        // Also check if click is on any part of the canvas workspace (including the gray background)
        const isOnCanvasWorkspace = target.closest('.bg-gray-100') && !target.closest('textarea');

        if (isOutsideTextarea && (isOnCanvas || isOnEditor || isOnCanvasContainer || isOnCanvasWorkspace)) {
          onChange(textarea.value);
          onClose();
        }
      }
    };

    // Handle canvas click outside event (for Konva elements)
    const handleCanvasClickOutside = () => {
      onChange(textarea.value);
      onClose();
    };

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        onChange(textarea.value);
        onClose();
      }
      if (e.key === 'Escape') {
        e.preventDefault();
        // Save changes even when pressing Escape
        onChange(textarea.value);
        onClose();
      }
    };

    const handleInput = () => {
      // Keep the same dimensions - don't change size during input
      // The size should remain consistent with the initial scale
    };

    // Add event listeners immediately
    textarea.addEventListener('keydown', handleKeyDown);
    textarea.addEventListener('input', handleInput);

    // Add outside click listener immediately using bubble phase
    window.addEventListener('click', handleOutsideClick, false);
    outsideClickAdded = true;

    // Add canvas click outside listener for Konva events
    window.addEventListener('canvas-click-outside', handleCanvasClickOutside);
    canvasClickAdded = true;

    return () => {
      // Remove event listeners
      textarea.removeEventListener('keydown', handleKeyDown);
      textarea.removeEventListener('input', handleInput);

      // Only remove outside click listener if it was added
      if (outsideClickAdded) {
        window.removeEventListener('click', handleOutsideClick, false);
      }

      // Remove canvas click listener if it was added
      if (canvasClickAdded) {
        window.removeEventListener('canvas-click-outside', handleCanvasClickOutside);
      }
    };
  }, [textNode, onChange, onClose]); // Removed region and initialText from dependencies

  const textValue = initialText !== undefined ? initialText : (textNode?.text() || "");

  return (
    <Html>
      <textarea
        ref={textareaRef}
        style={textareaStyles}
        defaultValue={textValue}
        placeholder="Enter translation..."
        autoFocus
      />
    </Html>
  );
}
